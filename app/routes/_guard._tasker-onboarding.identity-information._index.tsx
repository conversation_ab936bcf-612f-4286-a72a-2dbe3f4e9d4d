import { json } from '@remix-run/node';
import type { SerializeFrom } from '@remix-run/node';
import {
  isRouteErrorResponse,
  useLoaderData,
  useNavigate,
  useRouteError,
  useSearchParams,
  useSubmit,
} from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import {
  ConfirmUpdatingProfile,
  type ConfirmUpdatingProfileFormProps,
  TaskerProfileTabModel,
} from 'app/components/tasker-common';
import {
  PERMISSIONS,
  ROUTE_NAME,
  TASKER_PROFILE_TAB_ID,
  IDENTITY_INFORMATION_FOR_TASKER_TAB,
} from 'btaskee-constants';
import {
  BTaskeeTable,
  Badge,
  Breadcrumbs,
  BtaskeeResponseError,
  Button,
  DataTableColumnHeader,
  Grid,
  Typography,
  toast,
  useConfirm,
} from 'btaskee-ui';
import { getPageSizeAndPageIndex, getSkipAndLimit } from 'btaskee-utils';
import { format } from 'date-fns';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { hocAction, hocLoader } from '~/hoc/remix';
import i18next from '~/i18next.server';
import { IDENTITY_VERIFICATION_STATUS } from '~/services/constants.server';
import { getUserSession } from '~/services/helpers.server';
import { commitSession, getSession } from '~/services/session.server';
import {
  getTotalUsersForIdentityVerification,
  getUsersForIdentityVerification,
  updateIdentityVerificationStatusInUsers,
} from '~/services/tasker-profile.server';


export const loader = hocLoader(
  async ({ request }) => {
    const { isoCode } = await getUserSession({
      headers: request.headers,
    });

    const url = new URL(request.url);
    const tabId =
      url.searchParams.get('tabId') || TASKER_PROFILE_TAB_ID.VERIFYING;
    const search = url.searchParams.get('searchText') || '';

    // Find the tab configuration
    const tabConfig =
      IDENTITY_INFORMATION_FOR_TASKER_TAB.find(tab => tab.tabId === tabId) ||
      IDENTITY_INFORMATION_FOR_TASKER_TAB[0];

    const { skip, limit } = getSkipAndLimit(
      getPageSizeAndPageIndex({
        total: 1000, // We'll get the real total below
        pageSize: Number(url.searchParams.get('pageSize')) || 10,
        pageIndex: Number(url.searchParams.get('pageIndex')) || 0,
      }),
    );

    // Get total count
    const total = await getTotalUsersForIdentityVerification({
      isoCode,
      status: tabConfig.status,
      search,
    });

    // Get users data
    const users = await getUsersForIdentityVerification({
      isoCode,
      status: tabConfig.status,
      search,
      skip,
      limit,
    });

    return json({
      users,
      total,
      tabId: tabConfig.tabId,
      status: tabConfig.status,
      search,
    });
  },
  [PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER],
);

export const action = hocAction(
  async ({ request }) => {
    const { userId, username } = await getUserSession({
      headers: request.headers,
    });
    const language = await i18next.getLocale(request);
    const tTaskerProfile = await i18next.getFixedT(language, 'tasker-profile');

    const formData = await request.formData();
    const action = formData.get('action')?.toString();
    const targetUserId = formData.get('userId')?.toString();
    const status = formData
      .get('status')
      ?.toString() as `${IDENTITY_VERIFICATION_STATUS}`;
    const reason = formData.get('reason')?.toString();

    if (!action || !targetUserId || !status) {
      throw new Error('MISSING_REQUIRED_FIELDS');
    }

    if (action === 'updateStatus') {
      await updateIdentityVerificationStatusInUsers({
        userId: targetUserId,
        status,
        ...(reason ? { reason } : {}),
        updatedByUserId: userId,
        updatedByUsername: username,
      });

      const session = await getSession(request.headers.get('Cookie'));
      session.flash(
        'flashMessage',
        tTaskerProfile('IDENTITY_VERIFICATION_UPDATED_SUCCESSFULLY'),
      );

      return json(
        { msg: tTaskerProfile('IDENTITY_VERIFICATION_UPDATED_SUCCESSFULLY') },
        {
          headers: {
            'Set-Cookie': await commitSession(session),
          },
        },
      );
    }

    throw new Error('INVALID_ACTION');
  },
  [PERMISSIONS.WRITE_TASKER_PROFILE_ON_TASKER_ONBOARDING],
);

export function ErrorBoundary() {
  const error = useRouteError() as { message?: string };
  const { t: tCommon } = useTranslation('identity-information');

  if (isRouteErrorResponse(error)) {
    if (error?.message) {
      toast({ description: JSON.stringify(error.message) });
    }

    return <BtaskeeResponseError t={tCommon} errorStatus={error.status} />;
  }

  return <BtaskeeResponseError t={tCommon} errorStatus={500} />;
}

export default function IdentityInformation() {
  const { t: tTaskerProfile } = useTranslation('tasker-profile');
  const confirm = useConfirm();
  const submit = useSubmit();

  const [searchParams, setSearchParams] = useSearchParams();
  const loaderData = useLoaderData<typeof loader>();

  const navigate = useNavigate();

  // Form for updating profile status
  const { control, setValue, getValues } =
    useForm<ConfirmUpdatingProfileFormProps>({
      defaultValues: {
        reason: '',
        status: IDENTITY_VERIFICATION_STATUS.VERIFYING,
      },
    });

  // Mock reasons data - in real implementation, this should come from the loader
  const reasonsFromSetting = useMemo(
    () => [
      { name: 'INVALID_DOCUMENT', type: 'REJECTED' },
      { name: 'UNCLEAR_IMAGE', type: 'REJECTED' },
      { name: 'MISSING_INFORMATION', type: 'REJECTED' },
    ],
    [],
  );

  // Handler functions for approve/reject actions
  const handleApprove = useCallback(
    async (profile: { name: string; username?: string; _id: string }) => {
      setValue('status', IDENTITY_VERIFICATION_STATUS.APPROVED);

      const isConfirm = await confirm({
        title: tTaskerProfile('CONFIRM_APPROVE_IDENTITY'),
        body: (
          <ConfirmUpdatingProfile
            control={control}
            description={tTaskerProfile('CONFIRM_APPROVE_IDENTITY_DESCRIPTION')}
            setValue={setValue}
            taskerInfo={{
              taskerName: profile.name,
              username: profile.username || profile.name,
            }}
          />
        ),
        cancelButton: tTaskerProfile('CANCEL'),
        actionButton: tTaskerProfile('CONFIRM'),
      });

      if (isConfirm) {
        const formData = new FormData();
        const values = getValues();
        formData.append('action', 'updateStatus');
        formData.append('userId', profile._id);
        formData.append('status', values.status);
        if (values.reason) formData.append('reason', values.reason);

        submit(formData, { method: 'post' });
      }
    },
    [confirm, control, setValue, tTaskerProfile, getValues, submit],
  );

  const handleReject = useCallback(
    async (profile: { name: string; username?: string; _id: string }) => {
      setValue('status', IDENTITY_VERIFICATION_STATUS.NEEDS_UPDATE);
      setValue('reason', reasonsFromSetting[0]?.name);

      const isConfirm = await confirm({
        title: tTaskerProfile('CONFIRM_REJECT_IDENTITY'),
        body: (
          <ConfirmUpdatingProfile
            control={control}
            description={tTaskerProfile('CONFIRM_REJECT_IDENTITY_DESCRIPTION')}
            setValue={setValue}
            taskerInfo={{
              taskerName: profile.name,
              username: profile.username || profile.name,
            }}
            reasons={reasonsFromSetting.map(reason => reason.name)}
          />
        ),
        cancelButton: tTaskerProfile('CANCEL'),
        actionButton: tTaskerProfile('CONFIRM'),
      });

      if (isConfirm) {
        const formData = new FormData();
        const values = getValues();
        formData.append('action', 'updateStatus');
        formData.append('userId', profile._id);
        formData.append('status', values.status);
        if (values.reason) formData.append('reason', values.reason);

        submit(formData, { method: 'post' });
      }
    },
    [
      confirm,
      control,
      setValue,
      tTaskerProfile,
      getValues,
      reasonsFromSetting,
      submit,
    ],
  );

  const columns: ColumnDef<
    SerializeFrom<
      ReturnValueIgnorePromise<typeof getUsersForIdentityVerification>[0]
    >
  >[] = useMemo(
    () => [
      {
        accessorKey: 'username',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('USERNAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.username}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('TASKER_NAME')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.name}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('EMAIL')}
          />
        ),
        cell: ({ row }) => (
          <Typography variant="p">{row.original?.email}</Typography>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'createdAt',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('CREATED_AT')}
          />
        ),
        cell: ({ row }) =>
          row.original?.createdAt ? (
            <div className="whitespace-nowrap">
              {format(new Date(row.original.createdAt), 'HH:mm - dd/MM/yyyy')}
            </div>
          ) : null,
      },
      {
        accessorKey: 'identityCard',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('IDENTITY_CARD')}
          />
        ),
        cell: ({ row }) => {
          const status =
            row.original?.effectiveStatus ||
            IDENTITY_VERIFICATION_STATUS.NOT_UPLOADED;
          return (
            <div className="text-center">
              <Badge
                className={
                  status === IDENTITY_VERIFICATION_STATUS.APPROVED
                    ? 'bg-green-100 text-green-800'
                    : status === IDENTITY_VERIFICATION_STATUS.NEEDS_UPDATE
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                }>
                {tTaskerProfile(status)}
              </Badge>
            </div>
          );
        },
        enableSorting: false,
      },
      {
        accessorKey: 'uploadTimestamp',
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title={tTaskerProfile('UPLOAD_DATE')}
          />
        ),
        cell: ({ row }) =>
          row.original?.identityCard?.uploadTimestamp ? (
            <div className="whitespace-nowrap">
              {format(
                new Date(row.original.identityCard.uploadTimestamp),
                'HH:mm - dd/MM/yyyy',
              )}
            </div>
          ) : (
            <Typography variant="p" className="text-gray-400">
              {tTaskerProfile('NOT_UPLOADED')}
            </Typography>
          ),
      },
      {
        accessorKey: 'actions',
        header: () => (
          <div className="text-center">{tTaskerProfile('ACTIONS')}</div>
        ),
        cell: ({ row }) => {
          const status =
            row.original?.effectiveStatus ||
            IDENTITY_VERIFICATION_STATUS.NOT_UPLOADED;
          const canApprove =
            status === IDENTITY_VERIFICATION_STATUS.VERIFYING ||
            status === IDENTITY_VERIFICATION_STATUS.UPDATED;
          const canReject =
            status === IDENTITY_VERIFICATION_STATUS.VERIFYING ||
            status === IDENTITY_VERIFICATION_STATUS.UPDATED;

          return (
            <div className="flex justify-center gap-2">
              {canApprove && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-green-600 border-green-600 hover:bg-green-50"
                  onClick={e => {
                    e.stopPropagation();
                    handleApprove(row.original);
                  }}>
                  {tTaskerProfile('APPROVE')}
                </Button>
              )}
              {canReject && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-600 border-red-600 hover:bg-red-50"
                  onClick={e => {
                    e.stopPropagation();
                    handleReject(row.original);
                  }}>
                  {tTaskerProfile('REJECT')}
                </Button>
              )}
            </div>
          );
        },
        enableSorting: false,
      },
    ],
    [tTaskerProfile, handleApprove, handleReject],
  );

  return (
    <div className="flex flex-col gap-6">
      <Grid className="p-4 bg-secondary">
        <div className="grid rounded-xl">
          <Typography className="mb-3 capitalize" variant="h2">
            {tTaskerProfile('IDENTITY_INFORMATION_FOR_TASKER')}
          </Typography>
          <Breadcrumbs />
        </div>
      </Grid>
      <TaskerProfileTabModel
        items={IDENTITY_INFORMATION_FOR_TASKER_TAB}
        permissions={[PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER]}
        value={loaderData.tabId || ''}
        onValueChange={value => {
          setSearchParams(params => {
            params.set('tabId', value);

            const tabFound = IDENTITY_INFORMATION_FOR_TASKER_TAB.find(
              tab => tab.tabId === value,
            );

            if (tabFound) {
              params.set('status', tabFound.status?.[0]);
            }

            return params;
          });
        }}
      />
      <BTaskeeTable
        total={loaderData.total || 0}
        data={loaderData.users || []}
        columns={columns}
        search={{
          name: 'searchText',
          placeholder: tTaskerProfile('SEARCH_NAME_OR_EMAIL'),
          defaultValue: loaderData.search || '',
        }}
        isShowClearButton
        localeAddress="tasker-profile"
        onClickRow={profile =>
          navigate(
            `${ROUTE_NAME.IDENTITY_INFORMATION_FOR_TASKER}/${profile?._id}`,
          )
        }
        pagination={getPageSizeAndPageIndex({
          total: loaderData.total || 0,
          pageSize: Number(searchParams.get('pageSize') || 0),
          pageIndex: Number(searchParams.get('pageIndex') || 0),
        })}
      />
    </div>
  );
}
